﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\input.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\monitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\vulkan.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\window.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_joystick.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_monitor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_thread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_window.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\wgl_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\egl_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\osmesa_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\mappings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\glfw_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include\GLFW\glfw3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include\GLFW\glfw3native.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\win32_joystick.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\wgl_context.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\egl_context.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\osmesa_context.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B86C5E86-3326-3438-951D-175911F68351}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{6E3DDCDF-8F83-3312-A4FF-5A48F553962A}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
