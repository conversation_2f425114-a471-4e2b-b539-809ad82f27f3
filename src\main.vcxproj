﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FBF2C693-73B8-3819-9BA6-8B1AED45B04A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>main</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">main.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">main.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">main.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">main.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>opengl32.lib;..\third_party\imgui\Debug\imgui.lib;..\third_party\glfw\glfw\src\Debug\glfw3.lib;..\third_party\glad\Debug\glad.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/Debug/main.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/build/Debug/main.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>opengl32.lib;..\third_party\imgui\Release\imgui.lib;..\third_party\glfw\glfw\src\Release\glfw3.lib;..\third_party\glad\Release\glad.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/Release/main.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/build/Release/main.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>opengl32.lib;..\third_party\imgui\MinSizeRel\imgui.lib;..\third_party\glfw\glfw\src\MinSizeRel\glfw3.lib;..\third_party\glad\MinSizeRel\glad.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/MinSizeRel/main.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/build/MinSizeRel/main.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;IMGUI_IMPL_OPENGL_LOADER_GLAD;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\include;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\..;C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>opengl32.lib;..\third_party\imgui\RelWithDebInfo\imgui.lib;..\third_party\glfw\glfw\src\RelWithDebInfo\glfw3.lib;..\third_party\glad\RelWithDebInfo\glad.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/RelWithDebInfo/main.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/build/RelWithDebInfo/main.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp" />
    <ClInclude Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\ZERO_CHECK.vcxproj">
      <Project>{F4187D58-62D1-3755-A051-EAA164358EBC}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\glad.vcxproj">
      <Project>{DB5D0C2B-5A3B-36B1-83D0-7D441CA9AF85}</Project>
      <Name>glad</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glfw\glfw\src\glfw.vcxproj">
      <Project>{F919B6C3-8494-3322-A9F3-49FA1D0EFDE0}</Project>
      <Name>glfw</Name>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\imgui\imgui.vcxproj">
      <Project>{653941D0-D90C-3910-82F9-9473886A9224}</Project>
      <Name>imgui</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>