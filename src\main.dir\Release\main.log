﻿  Building Custom Rule C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/src/CMakeLists.txt
  main.cpp
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLint', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:             Derived=Eigen::Matrix<float,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\verasansmono_compressed.cpp(39,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\verasansmono_compressed.cpp(56,19): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(17,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(17,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(18,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(18,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(23,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(23,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,66): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(25,43): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(25,55): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(26,49): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(26,60): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,41): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,51): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,60): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(40,40): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(40,50): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(43,40): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(44,48): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(45,52): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(45,62): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(47,30): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(47,20): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(48,40): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(48,31): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,36): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,27): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,18): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(50,36): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(50,26): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,40): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,30): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,21): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,38): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,29): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,20): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,39): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,29): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,19): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,43): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,33): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,23): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(39,19): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(608,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(610,47): warning C4267: '+=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(625,43): warning C4244: '=': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(34,74): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(68,74): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(85,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,60): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,47): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,34): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,21): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,61): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,48): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,35): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,22): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(150,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(155,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(156,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'unsigned int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'unsigned int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(310,19): warning C4244: 'argument': conversion from 'unsigned int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(310,25): warning C4244: 'argument': conversion from 'unsigned int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(365,33): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(366,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(373,65): warning C4244: 'argument': conversion from 'double' to 'GLfloat', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(372,62): warning C4244: 'argument': conversion from 'double' to 'GLfloat', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         ]
  (compiling source file 'main.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\minwindef.h(130,9): warning C4005: 'APIENTRY': macro redefinition
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include\glad\glad.h(646,9):
      see previous definition of 'APIENTRY'
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(94,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(95,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(103,36): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(103,25): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(112,26): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(163,40): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(168,41): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,51): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,41): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,31): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(785,25): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(784,25): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(783,30): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(782,30): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(779,51): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(779,32): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(797,65): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(798,65): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(840,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(946,45): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(946,34): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(955,47): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(955,45): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1003,15): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1005,25): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1017,15): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1019,25): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1074,20): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1076,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1086,20): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1088,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.h(93,27): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiHelpers.h(40,45): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiHelpers.h(58,45): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.cpp(61,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.cpp(324,14): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(21,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(21,15):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(151,7):
          see reference to function template instantiation 'void igl::ortho<Eigen::Matrix<float,4,4,0,4,4>>(const float,const float,const float,const float,const float,const float,Eigen::PlainObjectBase<Eigen::Matrix<float,4,4,0,4,4>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(22,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(23,16): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(20,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(20,28):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(157,7):
          see reference to function template instantiation 'void igl::frustum<Eigen::Matrix<float,4,4,0,4,4>>(const float,const float,const float,const float,const float,const float,Eigen::PlainObjectBase<Eigen::Matrix<float,4,4,0,4,4>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(21,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(26,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\writeOBJ.cpp(33,21): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\writeOBJ.cpp(33,21):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(489,19):
          see reference to function template instantiation 'bool igl::writeOBJ<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(28,62): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(28,62):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(778,20):
          see reference to function template instantiation 'void igl::two_axis_valuator_fixed_up<float,float>(const int,const int,const double,const Eigen::Quaternion<float,0> &,const int,const int,const int,const int,Eigen::Quaternion<float,0> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(38,52): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244: 'argument': conversion from 'const double' to 'const Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(975,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<float,float>(const Eigen::Quaternion<float,0> &,const double,Eigen::Quaternion<float,0> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(501,10):
          see reference to function template instantiation 'void igl::per_face_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(47,10):
          see reference to function template instantiation 'void igl::per_face_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,3,1,0,3,1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Eigen::Matrix<double,3,1,0,3,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(22,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(22,9):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(419,18):
          see reference to function template instantiation 'bool igl::readOBJ<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(315,22):
          see reference to function template instantiation 'bool igl::list_to_matrix<igl::MshLoader::Float,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(112,17): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(112,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(435,17):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(69,10):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,std::string &,std::string &,std::string &,std::string &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(766,20):
          see reference to function template instantiation 'void igl::trackball<float,float>(const double,const double,const double,const Eigen::Quaternion<float,0> &,const double,const double,const double,const double,Eigen::Quaternion<float,0> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(148,10):
          see reference to function template instantiation 'void igl::trackball<Scalarquat>(const double,const double,const Q_type,const Q_type *,const double,const double,const double,const double,Q_type *)' being compiled
          with
          [
              Scalarquat=float,
              Q_type=float
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:             Derived=Eigen::Matrix<float,3,1,0,3,1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(797,39):
          see reference to function template instantiation 'Eigen::Matrix<float,3,1,0,3,1> igl::unproject<float>(const Eigen::Matrix<float,3,1,0,3,1> &,const Eigen::Matrix<float,4,4,0,4,4> &,const Eigen::Matrix<float,4,4,0,4,4> &,const Eigen::Matrix<float,4,1,0,4,1> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(65,3):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,3,1,0,3,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,3,1,0,3,1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:             Derived=Eigen::Matrix<float,3,1,0,3,1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305: 'initializing': truncation from 'double' to 'Q_type'
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(975,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<float,float>(const Eigen::Quaternion<float,0> &,const double,Eigen::Quaternion<float,0> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(105,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<Scalars>(const Q_type *,const Q_type,Q_type *)' being compiled
          with
          [
              Scalars=float,
              Q_type=float
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244: '+=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1621,22): warning C4244: 'initializing': conversion from 'double' to 'unsigned int', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1621,22):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(284,10):
          see reference to function template instantiation 'void igl::colormap<Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const igl::ColorMapType,const Eigen::MatrixBase<Derived> &,const double,const double,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,1,0,-1,1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1659,3):
          see reference to function template instantiation 'void igl::colormap<double>(const igl::ColorMapType,const T,T &,T &,T &)' being compiled
          with
          [
              T=double
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1585,7):
          see reference to function template instantiation 'void igl::colormap<T>(const double [][3],const T,T &,T &,T &)' being compiled
          with
          [
              T=double
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1622,21): warning C4244: 'initializing': conversion from 'double' to 'unsigned int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(502,10):
          see reference to function template instantiation 'void igl::per_vertex_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(119,5):
          see reference to function template instantiation 'void igl::per_vertex_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const igl::PerVertexNormalsWeightingType,const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(65,7):
          see reference to function template instantiation 'void igl::doublearea<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(53,56): warning C4267: 'argument': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(65,39): warning C4267: 'argument': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(170,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(170,22):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(308,23):
          see reference to function template instantiation 'bool igl::readOBJ<igl::MshLoader::Float,int>(const std::string,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(41,15):
          see reference to function template instantiation 'bool igl::readOBJ<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<std::tuple<std::string,Index,Index>,std::allocator<std::tuple<std::string,Index,Index>>> &)' being compiled
          with
          [
              Index=int
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(174,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(178,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(108,8): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(131,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(150,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(186,15): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(192,14): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(197,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(201,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(205,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(245,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:             Derived=Eigen::Matrix<float,1,3,1,1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(65,3):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,3,1,0,3,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,3,1,0,3,1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(31,5):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,1,3,1,1,3>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,1,3,1,1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:             Derived=Eigen::Matrix<float,1,3,1,1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(70,7):
          see reference to function template instantiation 'void igl::internal_angles<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,3,0,-1,3>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,3,0,-1,3>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\internal_angles.cpp(30,12):
          see reference to function template instantiation 'void igl::squared_edge_lengths<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(69,10):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,std::string &,std::string &,std::string &,std::string &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(102,7):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(113,10):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:             Derived=Eigen::CwiseBinaryOp<Eigen::internal::scalar_cmp_op<int,int,Eigen::internal::cmp_EQ>,const Eigen::ArrayWrapper<Eigen::Matrix<int,-1,1,0,-1,1>>,const Eigen::CwiseNullaryOp<Eigen::internal::scalar_constant_op<int>,Eigen::Array<int,-1,1,0,-1,1>>>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:             Derived=Eigen::CwiseBinaryOp<Eigen::internal::scalar_cmp_op<int,int,Eigen::internal::cmp_EQ>,const Eigen::ArrayWrapper<Eigen::Matrix<int,-1,1,0,-1,1>>,const Eigen::CwiseNullaryOp<Eigen::internal::scalar_constant_op<int>,Eigen::Array<int,-1,1,0,-1,1>>>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(58,3): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(58,3):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(119,12):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string &,FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(141,9):
          see reference to function template instantiation 'bool igl::readMESH<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(68,11): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(78,5): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(84,15): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(87,9): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(98,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(109,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(122,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(134,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(147,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(159,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(173,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(185,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(41,8): warning C4101: 'still_comments': unreferenced local variable
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(48,6): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(48,6):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(165,9):
          see reference to function template instantiation 'bool igl::readOFF<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(72,3): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(85,8): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(113,9): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(116,7): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(132,8): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(140,11): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(142,11): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(150,14): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(153,7): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(58,19): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(58,19):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(181,9):
          see reference to function template instantiation 'bool igl::readWRL<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(94,19): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\polygon_corners.cpp(28,18): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\polygon_corners.cpp(28,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(198,12):
          see reference to function template instantiation 'void igl::polygon_corners<int,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(72,14):
          see reference to function template instantiation 'void igl::doublearea<Derived,Eigen::Matrix<double,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,const double,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(162,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(74,30): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(78,30): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244: 'argument': conversion from 'Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:             Index=igl::doublearea::Index
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(168,3):
          see reference to function template instantiation 'bool igl::parallel_for<igl::doublearea::Index,igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>>(const Index,const FunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              FunctionType=igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(113,10):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(82,10):
          see reference to function template instantiation 'void igl::unique_rows<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::DenseBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(64,35): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(64,25): warning C4267: 'initializing': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(162,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(37,19):
          see reference to function template instantiation 'void igl::sort2<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(39,19):
          see reference to function template instantiation 'void igl::sort3<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(135,15): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(135,15):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(168,3):
          see reference to function template instantiation 'bool igl::parallel_for<igl::doublearea::Index,igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>>(const Index,const FunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              FunctionType=igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(111,10):
          see reference to function template instantiation 'bool igl::parallel_for<Index,igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>,igl::parallel_for::<lambda_866582420a11052db64bc10a9448d1c2>,igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>>(const Index,const PreFunctionType &,const FunctionType &,const AccumFunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              PreFunctionType=igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>,
              FunctionType=igl::parallel_for::<lambda_866582420a11052db64bc10a9448d1c2>,
              AccumFunctionType=igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(78,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(92,10):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(349,2): warning C4996: 'std::not_equal_to<int>::result_type': warning STL4007: Many result_type typedefs and all argument_type, first_argument_type, and second_argument_type typedefs are deprecated in C++17. You can define _SILENCE_CXX17_ADAPTOR_TYPEDEFS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(349,2):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(82,10):
          see reference to function template instantiation 'void igl::unique_rows<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::DenseBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(57,28):
          see reference to function template instantiation 'bool Eigen::MatrixBase<Derived>::operator !=<Derived>(const Eigen::MatrixBase<Derived> &) const' being compiled
          with
          [
              Derived=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\MatrixBase.h(297,14):
          see reference to class template instantiation 'Eigen::CwiseBinaryOp<std::not_equal_to<int>,const Derived,const Derived>' being compiled
          with
          [
              Derived=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\CwiseBinaryOp.h(78,10):
          see reference to class template instantiation 'Eigen::CwiseBinaryOpImpl<BinaryOp,LhsType,RhsType,Eigen::internal::cwise_promote_storage_type<Eigen::Dense,Eigen::Dense,BinaryOp>::ret>' being compiled
          with
          [
              BinaryOp=std::not_equal_to<int>,
              LhsType=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>,
              RhsType=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\XprHelper.h(480,73):
          see reference to class template instantiation 'Eigen::internal::traits<Derived>' being compiled
          with
          [
              Derived=Eigen::CwiseBinaryOp<std::not_equal_to<int>,const Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>,const Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\CwiseBinaryOp.h(38,23):
          see reference to class template instantiation 'Eigen::internal::result_of<BinaryOp (const int &,const int &)>' being compiled
          with
          [
              BinaryOp=std::not_equal_to<int>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(365,86):
          see reference to class template instantiation 'Eigen::internal::binary_result_of_select<Func,ArgType0,ArgType1,8>' being compiled
          with
          [
              Func=std::not_equal_to<int>,
              ArgType0=const int &,
              ArgType1=const int &
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(59,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(59,9):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(175,9):
          see reference to function template instantiation 'bool igl::readSTL<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(308,10):
          see reference to function template instantiation 'bool igl::readSTL<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(std::istream &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(31,8):
          see reference to function template instantiation 'bool igl::list_to_matrix<double,3,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(92,10):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(37,19):
          see reference to function template instantiation 'void igl::sort2<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         ]
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(39,19):
          see reference to function template instantiation 'void igl::sort3<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         ]
  (compiling source file 'main.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(107,16): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(107,16):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(27,8):
          see reference to function template instantiation 'bool igl::readSTL<double,int,double>(std::istream &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,std::vector<std::array<int,3>,std::allocator<std::array<int,3>>> &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(293,15):
          see reference to function template instantiation 'bool igl::read_stl_ascii<double,int,double>(std::istream &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,std::vector<std::array<int,3>,std::allocator<std::array<int,3>>> &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(135,18): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'main.cpp')
  
  mesh_gui_menu.cpp
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLint', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:             Derived=Eigen::Matrix<float,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\bind_vertex_attrib_array.cpp(21,35): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\verasansmono_compressed.cpp(39,21): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\verasansmono_compressed.cpp(56,19): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(222,33): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(230,44): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(235,46): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'GLsizei', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:             Derived=Eigen::Matrix<unsigned int,-1,-1,1,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\MeshGL.cpp(240,58): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(16,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(17,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(17,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(18,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(18,65): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(23,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(23,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,42): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,54): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(24,66): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(25,43): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(25,55): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(26,49): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(26,60): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,41): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,51): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(39,60): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(40,40): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(40,50): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(43,40): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(44,48): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(45,52): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(45,62): warning C4305: 'initializing': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(47,30): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(47,20): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(48,40): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(48,31): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,36): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,27): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(49,18): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(50,36): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(50,26): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,40): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,30): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(51,21): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,38): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,29): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(52,20): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,39): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,29): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(54,19): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,43): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,33): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\material_colors.h(55,23): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(39,19): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(336,14): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(384,15): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(408,14): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(434,14): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(568,35): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(569,35): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244: '=': conversion from '_Ty' to 'unsigned char', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:             _Ty=double
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(570,35): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(608,22): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(610,47): warning C4267: '+=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(625,43): warning C4244: '=': conversion from 'size_t' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(814,39): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244: '=': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(815,39): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(34,74): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(68,74): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(85,14): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,60): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,47): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,34): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(93,21): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,61): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,48): warning C4244: 'argument': conversion from 'float' to 'GLsizei', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,35): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(127,22): warning C4244: 'argument': conversion from 'float' to 'GLint', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(150,15): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(155,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(156,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'unsigned int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(264,18): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'unsigned int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:             Derived=Eigen::Matrix<unsigned char,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(265,19): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(310,19): warning C4244: 'argument': conversion from 'unsigned int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(310,25): warning C4244: 'argument': conversion from 'unsigned int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(365,33): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(366,22): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(373,65): warning C4244: 'argument': conversion from 'double' to 'GLfloat', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(372,62): warning C4244: 'argument': conversion from 'double' to 'GLfloat', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(175,46): warning C4267:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(191,63): warning C4267:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(219,42): warning C4267:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267: 'argument': conversion from 'size_t' to '_Ty', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:             _Ty=int
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\MshLoader.cpp(223,59): warning C4267:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\minwindef.h(130,9): warning C4005: 'APIENTRY': macro redefinition
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\glad\include\glad\glad.h(646,9):
      see previous definition of 'APIENTRY'
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(94,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(95,9): warning C4244: 'initializing': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(103,36): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(103,25): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(112,26): warning C4244: 'argument': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(163,40): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(168,41): warning C4244: '=': conversion from 'float' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,51): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,41): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(681,31): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(785,25): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(784,25): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(783,30): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(782,30): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(779,51): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(779,32): warning C4244: 'argument': conversion from 'float' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(797,65): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(798,65): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(840,18): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(946,45): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(946,34): warning C4244: 'argument': conversion from 'double' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(955,47): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(955,45): warning C4244: 'argument': conversion from 'int' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1003,15): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1005,25): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1017,15): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1019,25): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1074,20): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1076,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1086,20): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(1088,36): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.h(93,27): warning C4305: 'argument': truncation from 'double' to 'const float'
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiHelpers.h(40,45): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiHelpers.h(58,45): warning C4267: 'argument': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.cpp(61,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\imgui\ImGuiMenu.cpp(324,14): warning C4244: 'return': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(104,31): warning C4018: '<': signed/unsigned mismatch
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(161,52): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(161,52): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(161,52): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(161,52): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(161,52): warning C4244:         ]
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(166,52): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(166,52): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(166,52): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(166,52): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(166,52): warning C4244:         ]
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(171,52): warning C4244: 'argument': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(171,52): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(171,52): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(171,52): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\src\mesh_gui_menu.cpp(171,52): warning C4244:         ]
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(21,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(21,15):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(151,7):
          see reference to function template instantiation 'void igl::ortho<Eigen::Matrix<float,4,4,0,4,4>>(const float,const float,const float,const float,const float,const float,Eigen::PlainObjectBase<Eigen::Matrix<float,4,4,0,4,4>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(22,15): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\ortho.cpp(23,16): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(20,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(20,28):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerCore.cpp(157,7):
          see reference to function template instantiation 'void igl::frustum<Eigen::Matrix<float,4,4,0,4,4>>(const float,const float,const float,const float,const float,const float,Eigen::PlainObjectBase<Eigen::Matrix<float,4,4,0,4,4>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(21,28): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\frustum.cpp(26,38): warning C4244: '=': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\writeOBJ.cpp(33,21): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\writeOBJ.cpp(33,21):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(489,19):
          see reference to function template instantiation 'bool igl::writeOBJ<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(28,62): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(28,62):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(778,20):
          see reference to function template instantiation 'void igl::two_axis_valuator_fixed_up<float,float>(const int,const int,const double,const Eigen::Quaternion<float,0> &,const int,const int,const int,const int,Eigen::Quaternion<float,0> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\two_axis_valuator_fixed_up.cpp(38,52): warning C4244: 'argument': conversion from 'double' to 'const float', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244: 'argument': conversion from 'const double' to 'const Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(106,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(975,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<float,float>(const Eigen::Quaternion<float,0> &,const double,Eigen::Quaternion<float,0> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(21,13):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(501,10):
          see reference to function template instantiation 'void igl::per_face_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_face_normals.cpp(47,10):
          see reference to function template instantiation 'void igl::per_face_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,3,1,0,3,1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Eigen::Matrix<double,3,1,0,3,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(22,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(22,9):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(419,18):
          see reference to function template instantiation 'bool igl::readOBJ<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(315,22):
          see reference to function template instantiation 'bool igl::list_to_matrix<igl::MshLoader::Float,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(112,17): warning C4996: 'fopen': This function or variable may be unsafe. Consider using fopen_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(112,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(435,17):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(69,10):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,std::string &,std::string &,std::string &,std::string &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(121,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(766,20):
          see reference to function template instantiation 'void igl::trackball<float,float>(const double,const double,const double,const Eigen::Quaternion<float,0> &,const double,const double,const double,const double,Eigen::Quaternion<float,0> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(148,10):
          see reference to function template instantiation 'void igl::trackball<Scalarquat>(const double,const double,const Q_type,const Q_type *,const double,const double,const double,const double,Q_type *)' being compiled
          with
          [
              Scalarquat=float,
              Q_type=float
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(122,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(123,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(124,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(128,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(129,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(130,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\trackball.cpp(131,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:             Derived=Eigen::Matrix<float,3,1,0,3,1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(797,39):
          see reference to function template instantiation 'Eigen::Matrix<float,3,1,0,3,1> igl::unproject<float>(const Eigen::Matrix<float,3,1,0,3,1> &,const Eigen::Matrix<float,4,4,0,4,4> &,const Eigen::Matrix<float,4,4,0,4,4> &,const Eigen::Matrix<float,4,1,0,4,1> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(65,3):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,3,1,0,3,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,3,1,0,3,1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:             Derived=Eigen::Matrix<float,3,1,0,3,1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305: 'initializing': truncation from 'double' to 'Q_type'
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31): warning C4305:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(48,31):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\glfw\Viewer.cpp(975,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<float,float>(const Eigen::Quaternion<float,0> &,const double,Eigen::Quaternion<float,0> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(105,10):
          see reference to function template instantiation 'bool igl::snap_to_canonical_view_quat<Scalars>(const Q_type *,const Q_type,Q_type *)' being compiled
          with
          [
              Scalars=float,
              Q_type=float
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244: '+=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(63,61): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244: '=': conversion from 'double' to 'Q_type', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:             Q_type=float
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\snap_to_canonical_view_quat.cpp(92,22): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1621,22): warning C4244: 'initializing': conversion from 'double' to 'unsigned int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1621,22):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(284,10):
          see reference to function template instantiation 'void igl::colormap<Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const igl::ColorMapType,const Eigen::MatrixBase<Derived> &,const double,const double,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,1,0,-1,1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1659,3):
          see reference to function template instantiation 'void igl::colormap<double>(const igl::ColorMapType,const T,T &,T &,T &)' being compiled
          with
          [
              T=double
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1585,7):
          see reference to function template instantiation 'void igl::colormap<T>(const double [][3],const T,T &,T &,T &)' being compiled
          with
          [
              T=double
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\colormap.cpp(1622,21): warning C4244: 'initializing': conversion from 'double' to 'unsigned int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\opengl\ViewerData.cpp(502,10):
          see reference to function template instantiation 'void igl::per_vertex_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(119,5):
          see reference to function template instantiation 'void igl::per_vertex_normals<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Derived,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,const igl::PerVertexNormalsWeightingType,const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(65,7):
          see reference to function template instantiation 'void igl::doublearea<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:             Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(25,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(53,56): warning C4267: 'argument': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(65,39): warning C4267: 'argument': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(170,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(170,22):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(308,23):
          see reference to function template instantiation 'bool igl::readOBJ<igl::MshLoader::Float,int>(const std::string,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(41,15):
          see reference to function template instantiation 'bool igl::readOBJ<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<std::tuple<std::string,Index,Index>,std::allocator<std::tuple<std::string,Index,Index>>> &)' being compiled
          with
          [
              Index=int
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(174,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(178,22): warning C4267: 'return': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(108,8): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(131,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(150,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(186,15): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(192,14): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(197,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(201,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(205,20): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOBJ.cpp(245,9): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:             Derived=Eigen::Matrix<float,1,3,1,1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,25):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(65,3):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,3,1,0,3,1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,3,1,0,3,1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(31,5):
          see reference to function template instantiation 'void igl::unproject<Derived,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,4,0,4,4>,Eigen::Matrix<float,4,1,0,4,1>,Eigen::Matrix<float,1,3,1,1,3>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,4,0,4,4>> &,const Eigen::MatrixBase<Eigen::Matrix<float,4,1,0,4,1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<float,1,3,1,1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:             Derived=Eigen::Matrix<float,1,3,1,1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unproject.cpp(36,15): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,23):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\per_vertex_normals.cpp(70,7):
          see reference to function template instantiation 'void igl::internal_angles<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,3,0,-1,3>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,3,0,-1,3>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\internal_angles.cpp(30,12):
          see reference to function template instantiation 'void igl::squared_edge_lengths<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,const Eigen::MatrixBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\squared_edge_lengths.cpp(19,15): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,34):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(69,10):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,std::string &,std::string &,std::string &,std::string &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(102,7):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(113,10):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(33,26): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:             Derived=Eigen::CwiseBinaryOp<Eigen::internal::scalar_cmp_op<int,int,Eigen::internal::cmp_EQ>,const Eigen::ArrayWrapper<Eigen::Matrix<int,-1,1,0,-1,1>>,const Eigen::CwiseNullaryOp<Eigen::internal::scalar_constant_op<int>,Eigen::Array<int,-1,1,0,-1,1>>>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,40): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:             Derived=Eigen::CwiseBinaryOp<Eigen::internal::scalar_cmp_op<int,int,Eigen::internal::cmp_EQ>,const Eigen::ArrayWrapper<Eigen::Matrix<int,-1,1,0,-1,1>>,const Eigen::CwiseNullaryOp<Eigen::internal::scalar_constant_op<int>,Eigen::Array<int,-1,1,0,-1,1>>>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(86,18): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(58,3): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(58,3):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(119,12):
          see reference to function template instantiation 'bool igl::read_triangle_mesh<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const std::string &,FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(141,9):
          see reference to function template instantiation 'bool igl::readMESH<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(68,11): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(78,5): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(84,15): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(87,9): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(98,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(109,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(122,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(134,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(147,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(159,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(173,15): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(185,17): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readMESH.cpp(41,8): warning C4101: 'still_comments': unreferenced local variable
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(48,6): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(48,6):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(165,9):
          see reference to function template instantiation 'bool igl::readOFF<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(72,3): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(85,8): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(113,9): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(116,7): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(132,8): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(140,11): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(142,11): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(150,14): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readOFF.cpp(153,7): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(58,19): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(58,19):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(181,9):
          see reference to function template instantiation 'bool igl::readWRL<igl::MshLoader::Float,int>(FILE *,std::vector<igl::MshLoader::FloatVector,std::allocator<igl::MshLoader::FloatVector>> &,std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readWRL.cpp(94,19): warning C4996: 'fscanf': This function or variable may be unsafe. Consider using fscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\polygon_corners.cpp(28,18): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\polygon_corners.cpp(28,18):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(198,12):
          see reference to function template instantiation 'void igl::polygon_corners<int,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const std::vector<igl::MshLoader::IntVector,std::allocator<igl::MshLoader::IntVector>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(72,14):
          see reference to function template instantiation 'void igl::doublearea<Derived,Eigen::Matrix<double,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,const double,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(162,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(74,30): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(78,30): warning C4267: '=': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244: 'argument': conversion from 'Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:             Index=igl::doublearea::Index
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(110,62):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(168,3):
          see reference to function template instantiation 'bool igl::parallel_for<igl::doublearea::Index,igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>>(const Index,const FunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              FunctionType=igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,34):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(113,10):
          see reference to function template instantiation 'void igl::boundary_facets<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::MatrixBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(82,10):
          see reference to function template instantiation 'void igl::unique_rows<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::DenseBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(30,22): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,34): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'const int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(31,22): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(64,35): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(64,25): warning C4267: 'initializing': conversion from 'size_t' to 'const int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(162,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(37,19):
          see reference to function template instantiation 'void igl::sort2<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(39,19):
          see reference to function template instantiation 'void igl::sort3<Derived,Eigen::Matrix<double,-1,3,0,-1,3>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<double,-1,3,0,-1,3>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:             Derived=Eigen::Matrix<double,-1,3,0,-1,3>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(135,15): warning C4018: '<': signed/unsigned mismatch
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(135,15):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\doublearea.cpp(168,3):
          see reference to function template instantiation 'bool igl::parallel_for<igl::doublearea::Index,igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>>(const Index,const FunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              FunctionType=igl::doublearea::<lambda_68877b35a93cbac5e1ff095c5c17bfef>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\parallel_for.h(111,10):
          see reference to function template instantiation 'bool igl::parallel_for<Index,igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>,igl::parallel_for::<lambda_866582420a11052db64bc10a9448d1c2>,igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>>(const Index,const PreFunctionType &,const FunctionType &,const AccumFunctionType &,const size_t)' being compiled
          with
          [
              Index=igl::doublearea::Index,
              PreFunctionType=igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>,
              FunctionType=igl::parallel_for::<lambda_866582420a11052db64bc10a9448d1c2>,
              AccumFunctionType=igl::parallel_for::<lambda_946a5f5e8ace55b1c74f1e4340b12c92>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(30,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(78,8):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(92,10):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(43,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(349,2): warning C4996: 'std::not_equal_to<int>::result_type': warning STL4007: Many result_type typedefs and all argument_type, first_argument_type, and second_argument_type typedefs are deprecated in C++17. You can define _SILENCE_CXX17_ADAPTOR_TYPEDEFS_DEPRECATION_WARNING or _SILENCE_ALL_CXX17_DEPRECATION_WARNINGS to suppress this warning.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(349,2):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\boundary_facets.cpp(82,10):
          see reference to function template instantiation 'void igl::unique_rows<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,1,0,-1,1>,Eigen::Matrix<int,-1,1,0,-1,1>>(const Eigen::DenseBase<Derived> &,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,1,0,-1,1>> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\unique_rows.cpp(57,28):
          see reference to function template instantiation 'bool Eigen::MatrixBase<Derived>::operator !=<Derived>(const Eigen::MatrixBase<Derived> &) const' being compiled
          with
          [
              Derived=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\MatrixBase.h(297,14):
          see reference to class template instantiation 'Eigen::CwiseBinaryOp<std::not_equal_to<int>,const Derived,const Derived>' being compiled
          with
          [
              Derived=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\CwiseBinaryOp.h(78,10):
          see reference to class template instantiation 'Eigen::CwiseBinaryOpImpl<BinaryOp,LhsType,RhsType,Eigen::internal::cwise_promote_storage_type<Eigen::Dense,Eigen::Dense,BinaryOp>::ret>' being compiled
          with
          [
              BinaryOp=std::not_equal_to<int>,
              LhsType=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>,
              RhsType=Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\XprHelper.h(480,73):
          see reference to class template instantiation 'Eigen::internal::traits<Derived>' being compiled
          with
          [
              Derived=Eigen::CwiseBinaryOp<std::not_equal_to<int>,const Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>,const Eigen::Block<Eigen::Matrix<int,-1,-1,0,-1,-1>,1,-1,false>>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\CwiseBinaryOp.h(38,23):
          see reference to class template instantiation 'Eigen::internal::result_of<BinaryOp (const int &,const int &)>' being compiled
          with
          [
              BinaryOp=std::not_equal_to<int>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\eigen\eigen\Eigen\src\Core\util\Meta.h(365,86):
          see reference to class template instantiation 'Eigen::internal::binary_result_of_select<Func,ArgType0,ArgType1,8>' being compiled
          with
          [
              Func=std::not_equal_to<int>,
              ArgType0=const int &,
              ArgType1=const int &
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(59,9): warning C4267: 'initializing': conversion from 'size_t' to 'int', possible loss of data
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\list_to_matrix.cpp(59,9):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\read_triangle_mesh.cpp(175,9):
          see reference to function template instantiation 'bool igl::readSTL<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(FILE *,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(308,10):
          see reference to function template instantiation 'bool igl::readSTL<Eigen::Matrix<double,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<double,-1,-1,0,-1,-1>>(std::istream &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<int,-1,-1,0,-1,-1>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(31,8):
          see reference to function template instantiation 'bool igl::list_to_matrix<double,3,Eigen::Matrix<double,-1,-1,0,-1,-1>>(const std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,Eigen::PlainObjectBase<Eigen::Matrix<double,-1,-1,0,-1,-1>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(178,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(92,10):
          see reference to function template instantiation 'void igl::sort<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(37,19):
          see reference to function template instantiation 'void igl::sort2<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(180,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(224,17):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(39,19):
          see reference to function template instantiation 'void igl::sort3<Derived,Eigen::Matrix<int,-1,-1,0,-1,-1>,Eigen::Matrix<int,-1,-1,0,-1,-1>>(const Eigen::DenseBase<Derived> &,const int,const bool,Eigen::PlainObjectBase<Derived> &,Eigen::PlainObjectBase<Derived> &)' being compiled
          with
          [
              Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
          ]
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244: 'initializing': conversion from 'Eigen::EigenBase<Derived>::Index' to 'int', possible loss of data
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         with
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         [
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:             Derived=Eigen::Matrix<int,-1,-1,0,-1,-1>
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\sort.cpp(226,17): warning C4244:         ]
  (compiling source file 'mesh_gui_menu.cpp')
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(107,16): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
      C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(107,16):
      the template instantiation context (the oldest one first) is
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(27,8):
          see reference to function template instantiation 'bool igl::readSTL<double,int,double>(std::istream &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,std::vector<std::array<int,3>,std::allocator<std::array<int,3>>> &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &)' being compiled
          C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(293,15):
          see reference to function template instantiation 'bool igl::read_stl_ascii<double,int,double>(std::istream &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &,std::vector<std::array<int,3>,std::allocator<std::array<int,3>>> &,std::vector<std::array<double,3>,std::allocator<std::array<double,3>>> &)' being compiled
  
C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\third_party\libigl\libigl\include\igl\readSTL.cpp(135,18): warning C4996: 'sscanf': This function or variable may be unsafe. Consider using sscanf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
  (compiling source file 'mesh_gui_menu.cpp')
  
  Generating Code...
  main.vcxproj -> C:\Users\<USER>\Desktop\Squaremind\SquareMind_Cpp\build\Release\main.exe
  'pwsh.exe' is not recognized as an internal or external command,
  operable program or batch file.
