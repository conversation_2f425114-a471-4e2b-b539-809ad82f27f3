^C:\USERS\<USER>\DESKTOP\SQUAREMIND\SQUAREMIND_CPP\CMAKEFILES\BCE36F3EFF73B5D75BB440129AF4755F\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/SquareMindCpp.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
