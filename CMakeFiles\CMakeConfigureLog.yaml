
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.18+a338add32 for .NET Framework
      Build started 8/29/2025 1:04:39 PM.
      
      Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.65
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/3.30.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.18+a338add32 for .NET Framework
      Build started 8/29/2025 1:04:41 PM.
      
      Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
        'pwsh.exe' is not recognized as an internal or external command,
        operable program or batch file.
        The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"" exited with code 9009.
        "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\3.30.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:01.03
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/3.30.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-mgvfr2"
      binary: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-mgvfr2"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-mgvfr2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_25a3b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.18+a338add32 for .NET Framework
        Build started 8/29/2025 1:04:43 PM.
        
        Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\cmTC_25a3b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_25a3b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\".
          Creating directory "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_25a3b.dir\\Debug\\\\" /Fd"cmTC_25a3b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_25a3b.dir\\Debug\\\\" /Fd"cmTC_25a3b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\cmTC_25a3b.exe" /INCREMENTAL /ILK:"cmTC_25a3b.dir\\Debug\\cmTC_25a3b.ilk" /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-mgvfr2/Debug/cmTC_25a3b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-mgvfr2/Debug/cmTC_25a3b.lib" /MACHINE:X64  /machine:x64 cmTC_25a3b.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_25a3b.vcxproj -> C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\cmTC_25a3b.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\cmTC_25a3b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\cmTC_25a3b.write.1u.tlog" "cmTC_25a3b.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' is not recognized as an internal or external command,
          operable program or batch file.
          The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\cmTC_25a3b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\cmTC_25a3b.write.1u.tlog" "cmTC_25a3b.dir\\Debug\\vcpkg.applocal.log"" exited with code 9009.
          "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\Debug\\cmTC_25a3b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\cmTC_25a3b.write.1u.tlog" "cmTC_25a3b.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\unsuccessfulbuild".
          Touching "cmTC_25a3b.dir\\Debug\\cmTC_25a3b.tlog\\cmTC_25a3b.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-mgvfr2\\cmTC_25a3b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.04
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35214.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-k2lxbs"
      binary: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-k2lxbs"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-k2lxbs'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1ff9b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.18+a338add32 for .NET Framework
        Build started 8/29/2025 1:04:44 PM.
        
        Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\cmTC_1ff9b.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_1ff9b.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\".
          Creating directory "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_1ff9b.dir\\Debug\\\\" /Fd"cmTC_1ff9b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_1ff9b.dir\\Debug\\\\" /Fd"cmTC_1ff9b.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\cmTC_1ff9b.exe" /INCREMENTAL /ILK:"cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.ilk" /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-k2lxbs/Debug/cmTC_1ff9b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-k2lxbs/Debug/cmTC_1ff9b.lib" /MACHINE:X64  /machine:x64 cmTC_1ff9b.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_1ff9b.vcxproj -> C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\cmTC_1ff9b.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\cmTC_1ff9b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\cmTC_1ff9b.write.1u.tlog" "cmTC_1ff9b.dir\\Debug\\vcpkg.applocal.log"
          'pwsh.exe' is not recognized as an internal or external command,
          operable program or batch file.
          The command "pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\cmTC_1ff9b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\cmTC_1ff9b.write.1u.tlog" "cmTC_1ff9b.dir\\Debug\\vcpkg.applocal.log"" exited with code 9009.
          "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe" -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\Debug\\cmTC_1ff9b.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\cmTC_1ff9b.write.1u.tlog" "cmTC_1ff9b.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          Deleting file "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\unsuccessfulbuild".
          Touching "cmTC_1ff9b.dir\\Debug\\cmTC_1ff9b.tlog\\cmTC_1ff9b.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-k2lxbs\\cmTC_1ff9b.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.07
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35214.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CheckCSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "CMakeLists.txt:23 (find_package)"
    directories:
      source: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-scirhv"
      binary: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-scirhv"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-scirhv'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_fc08d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.18+a338add32 for .NET Framework
        Build started 8/29/2025 1:04:45 PM.
        
        Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\cmTC_fc08d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_fc08d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\Debug\\".
          Creating directory "cmTC_fc08d.dir\\Debug\\cmTC_fc08d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_fc08d.dir\\Debug\\cmTC_fc08d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_fc08d.dir\\Debug\\cmTC_fc08d.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fc08d.dir\\Debug\\\\" /Fd"cmTC_fc08d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_fc08d.dir\\Debug\\\\" /Fd"cmTC_fc08d.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\src.c"
          src.c
        C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\cmTC_fc08d.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\cmTC_fc08d.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\cmTC_fc08d.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-scirhv\\cmTC_fc08d.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.39
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "CMakeLists.txt:23 (find_package)"
    directories:
      source: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-zgz1f4"
      binary: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-zgz1f4"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-zgz1f4'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e3541.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.18+a338add32 for .NET Framework
        Build started 8/29/2025 1:04:46 PM.
        
        Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\cmTC_e3541.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e3541.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\Debug\\".
          Creating directory "cmTC_e3541.dir\\Debug\\cmTC_e3541.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e3541.dir\\Debug\\cmTC_e3541.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e3541.dir\\Debug\\cmTC_e3541.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e3541.dir\\Debug\\\\" /Fd"cmTC_e3541.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e3541.dir\\Debug\\\\" /Fd"cmTC_e3541.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\Debug\\cmTC_e3541.exe" /INCREMENTAL /ILK:"cmTC_e3541.dir\\Debug\\cmTC_e3541.ilk" /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-zgz1f4/Debug/cmTC_e3541.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-zgz1f4/Debug/cmTC_e3541.lib" /MACHINE:X64  /machine:x64 cmTC_e3541.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\cmTC_e3541.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\cmTC_e3541.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\cmTC_e3541.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-zgz1f4\\cmTC_e3541.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.46
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "CMakeLists.txt:23 (find_package)"
    directories:
      source: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-j5jdck"
      binary: "C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-j5jdck"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-j5jdck'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7930e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.18+a338add32 for .NET Framework
        Build started 8/29/2025 1:04:47 PM.
        
        Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\cmTC_7930e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7930e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\Debug\\".
          Creating directory "cmTC_7930e.dir\\Debug\\cmTC_7930e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7930e.dir\\Debug\\cmTC_7930e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7930e.dir\\Debug\\cmTC_7930e.tlog\\unsuccessfulbuild".
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7930e.dir\\Debug\\\\" /Fd"cmTC_7930e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35214 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_7930e.dir\\Debug\\\\" /Fd"cmTC_7930e.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\Debug\\cmTC_7930e.exe" /INCREMENTAL /ILK:"cmTC_7930e.dir\\Debug\\cmTC_7930e.ilk" /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-j5jdck/Debug/cmTC_7930e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/CMakeFiles/CMakeScratch/TryCompile-j5jdck/Debug/cmTC_7930e.lib" /MACHINE:X64  /machine:x64 cmTC_7930e.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\cmTC_7930e.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\cmTC_7930e.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\cmTC_7930e.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\Desktop\\Squaremind\\SquareMind_Cpp\\CMakeFiles\\CMakeScratch\\TryCompile-j5jdck\\cmTC_7930e.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.47
        
      exitCode: 1
...
