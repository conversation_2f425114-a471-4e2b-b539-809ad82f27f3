/*!

@page compile_guide Compiling GLFW

@tableofcontents

This is about compiling the GLFW library itself.  For information on how to
build applications that use GLFW, see @ref build_guide.


@section compile_cmake Using CMake

GLFW uses [CMake](https://cmake.org/) to generate project files or makefiles
for a particular development environment.  If you are on a Unix-like system such
as Linux or FreeBSD or have a package system like Fink, MacPorts, Cygwin or
Homebrew, you can install its CMake package.  If not, you can download
installers for Windows and macOS from the
[CMake website](https://cmake.org/).

@note CMake only generates project files or makefiles.  It does not compile the
actual GLFW library.  To compile GLFW, first generate these files for your
chosen development environment and then use them to compile the actual GLFW
library.


@subsection compile_deps Dependencies

Once you have installed CMake, make sure that all other dependencies are
available.  On some platforms, GLF<PERSON> needs a few additional packages to be
installed.  See the section for your chosen platform and development environment
below.


@subsubsection compile_deps_msvc Dependencies for Visual C++ on Windows

The Windows SDK bundled with Visual C++ already contains all the necessary
headers, link libraries and tools except for CMake.  Move on to @ref
compile_generate.


@subsubsection compile_deps_mingw Dependencies for MinGW or MinGW-w64 on Windows

Both the MinGW and the MinGW-w64 packages already contain all the necessary
headers, link libraries and tools except for CMake.  Move on to @ref
compile_generate.


@subsubsection compile_deps_mingw_cross Dependencies for MinGW or MinGW-w64 cross-compilation

Both Cygwin and many Linux distributions have MinGW or MinGW-w64 packages.  For
example, Cygwin has the `mingw64-i686-gcc` and `mingw64-x86_64-gcc` packages
for 32- and 64-bit version of MinGW-w64, while Debian GNU/Linux and derivatives
like Ubuntu have the `mingw-w64` package for both.

GLFW has CMake toolchain files in the `CMake/` directory that set up
cross-compilation of Windows binaries.  To use these files you add an option
when running `cmake` to generate the project files or makefiles:

@code{.sh}
cmake -DCMAKE_TOOLCHAIN_FILE=<toolchain-file> .
@endcode

The exact toolchain file to use depends on the prefix used by the MinGW or
MinGW-w64 binaries on your system.  You can usually see this in the /usr
directory.  For example, both the Debian/Ubuntu and Cygwin MinGW-w64 packages
have `/usr/x86_64-w64-mingw32` for the 64-bit compilers, so the correct
invocation would be:

@code{.sh}
cmake -DCMAKE_TOOLCHAIN_FILE=CMake/x86_64-w64-mingw32.cmake .
@endcode

For more details see the article
[CMake Cross Compiling](https://gitlab.kitware.com/cmake/community/wikis/doc/cmake/CrossCompiling) on
the CMake wiki.

Once you have this set up, move on to @ref compile_generate.


@subsubsection compile_deps_xcode Dependencies for Xcode on macOS

Xcode comes with all necessary tools except for CMake.  The required headers
and libraries are included in the core macOS frameworks.  Xcode can be
downloaded from the Mac App Store or from the ADC Member Center.

Once you have Xcode installed, move on to @ref compile_generate.


@subsubsection compile_deps_x11 Dependencies for Linux and X11

To compile GLFW for X11, you need to have the X11 packages installed, as well as
the basic development tools like GCC and make.  For example, on Ubuntu and other
distributions based on Debian GNU/Linux, you need to install the `xorg-dev`
package, which pulls in all X.org header packages.

Once you have installed the necessary packages, move on to @ref
compile_generate.


@subsubsection compile_deps_wayland Dependencies for Linux and Wayland

To compile GLFW for Wayland, you need to have the Wayland packages installed,
as well as the basic development tools like GCC and make.  For example, on
Ubuntu and other distributions based on Debian GNU/Linux, you need to install
the `libwayland-dev` package, which contains all Wayland headers and pulls in
wayland-scanner, as well as the `wayland-protocols` and `extra-cmake-modules`
packages.

Once you have installed the necessary packages, move on to @ref
compile_generate.


@subsection compile_deps_osmesa Dependencies for Linux and OSMesa

To compile GLFW for OSMesa, you need to install the OSMesa library and header
packages.  For example, on Ubuntu and other distributions based on Debian
GNU/Linux, you need to install the `libosmesa6-dev` package.  The OSMesa library
is required at runtime for context creation and is loaded on demand.

Once you have installed the necessary packages, move on to @ref
compile_generate.


@subsection compile_generate Generating build files with CMake

Once you have all necessary dependencies it is time to generate the project
files or makefiles for your development environment.  CMake needs to know two
paths for this: the path to the _root_ directory of the GLFW source tree (i.e.
_not_ the `src` subdirectory) and the target path for the generated files and
compiled binaries.  If these are the same, it is called an in-tree build,
otherwise it is called an out-of-tree build.

One of several advantages of out-of-tree builds is that you can generate files
and compile for different development environments using a single source tree.

@note This section is about generating the project files or makefiles necessary
to compile the GLFW library, not about compiling the actual library.


@subsubsection compile_generate_cli Generating files with the CMake command-line tool

To make an in-tree build, enter the _root_ directory of the GLFW source tree
(i.e. _not_ the `src` subdirectory) and run CMake.  The current directory is
used as target path, while the path provided as an argument is used to find the
source tree.

@code{.sh}
cd <glfw-root-dir>
cmake .
@endcode

To make an out-of-tree build, make a directory outside of the source tree, enter
it and run CMake with the (relative or absolute) path to the root of the source
tree as an argument.

@code{.sh}
mkdir glfw-build
cd glfw-build
cmake <glfw-root-dir>
@endcode

Once you have generated the project files or makefiles for your chosen
development environment, move on to @ref compile_compile.


@subsubsection compile_generate_gui Generating files with the CMake GUI

If you are using the GUI version, choose the root of the GLFW source tree as
source location and the same directory or another, empty directory as the
destination for binaries.  Choose _Configure_, change any options you wish to,
_Configure_ again to let the changes take effect and then _Generate_.

Once you have generated the project files or makefiles for your chosen
development environment, move on to @ref compile_compile.


@subsection compile_compile Compiling the library

You should now have all required dependencies and the project files or makefiles
necessary to compile GLFW.  Go ahead and compile the actual GLFW library with
these files, as you would with any other project.

Once the GLFW library is compiled, you are ready to build your applications,
linking it to the GLFW library.  See @ref build_guide for more information.


@subsection compile_options CMake options

The CMake files for GLFW provide a number of options, although not all are
available on all supported platforms.  Some of these are de facto standards
among projects using CMake and so have no `GLFW_` prefix.

If you are using the GUI version of CMake, these are listed and can be changed
from there.  If you are using the command-line version of CMake you can use the
`ccmake` ncurses GUI to set options.  Some package systems like Ubuntu and other
distributions based on Debian GNU/Linux have this tool in a separate
`cmake-curses-gui` package.

Finally, if you don't want to use any GUI, you can set options from the `cmake`
command-line with the `-D` flag.

@code{.sh}
cmake -DBUILD_SHARED_LIBS=ON .
@endcode


@subsubsection compile_options_shared Shared CMake options

@anchor BUILD_SHARED_LIBS
__BUILD_SHARED_LIBS__ determines whether GLFW is built as a static
library or as a DLL / shared library / dynamic library.

@anchor GLFW_BUILD_EXAMPLES
__GLFW_BUILD_EXAMPLES__ determines whether the GLFW examples are built
along with the library.

@anchor GLFW_BUILD_TESTS
__GLFW_BUILD_TESTS__ determines whether the GLFW test programs are
built along with the library.

@anchor GLFW_BUILD_DOCS
__GLFW_BUILD_DOCS__ determines whether the GLFW documentation is built along
with the library.

@anchor GLFW_VULKAN_STATIC
__GLFW_VULKAN_STATIC__ determines whether to use the Vulkan loader linked
directly with the application.


@subsubsection compile_options_win32 Windows specific CMake options

@anchor USE_MSVC_RUNTIME_LIBRARY_DLL
__USE_MSVC_RUNTIME_LIBRARY_DLL__ determines whether to use the DLL version or the
static library version of the Visual C++ runtime library.  If set to `ON`, the
DLL version of the Visual C++ library is used.

@anchor GLFW_USE_HYBRID_HPG
__GLFW_USE_HYBRID_HPG__ determines whether to export the `NvOptimusEnablement` and
`AmdPowerXpressRequestHighPerformance` symbols, which force the use of the
high-performance GPU on Nvidia Optimus and AMD PowerXpress systems.  These symbols
need to be exported by the EXE to be detected by the driver, so the override
will not work if GLFW is built as a DLL.


@section compile_manual Compiling GLFW manually

If you wish to compile GLFW without its CMake build environment then you will
have to do at least some of the platform detection yourself.  GLFW needs
a configuration macro to be defined in order to know what window system it's
being compiled for and also has optional, platform-specific ones for various
features.

When building with CMake, the `glfw_config.h` configuration header is generated
based on the current platform and CMake options.  The GLFW CMake environment
defines @b GLFW_USE_CONFIG_H, which causes this header to be included by
`internal.h`.  Without this macro, GLFW will expect the necessary configuration
macros to be defined on the command-line.

The window creation API is used to create windows, handle input, monitors, gamma
ramps and clipboard.  The options are:

 - @b _GLFW_COCOA to use the Cocoa frameworks
 - @b _GLFW_WIN32 to use the Win32 API
 - @b _GLFW_X11 to use the X Window System
 - @b _GLFW_WAYLAND to use the Wayland API (experimental and incomplete)
 - @b _GLFW_OSMESA to use the OSMesa API (headless and non-interactive)

If you are building GLFW as a shared library / dynamic library / DLL then you
must also define @b _GLFW_BUILD_DLL.  Otherwise, you must not define it.

If you are linking the Vulkan loader directly with your application then you
must also define @b _GLFW_VULKAN_STATIC.  Otherwise, GLFW will attempt to use the
external version.

If you are using a custom name for the Vulkan, EGL, GLX, OSMesa, OpenGL, GLESv1
or GLESv2 library, you can override the default names by defining those you need
of @b _GLFW_VULKAN_LIBRARY, @b _GLFW_EGL_LIBRARY, @b _GLFW_GLX_LIBRARY, @b
_GLFW_OSMESA_LIBRARY, @b _GLFW_OPENGL_LIBRARY, @b _GLFW_GLESV1_LIBRARY and @b
_GLFW_GLESV2_LIBRARY.  Otherwise, GLFW will use the built-in default names.

For the EGL context creation API, the following options are available:

 - @b _GLFW_USE_EGLPLATFORM_H to use an existing `EGL/eglplatform.h` header file
   for native handle types (fallback)

@note None of the @ref build_macros may be defined during the compilation of
GLFW.  If you define any of these in your build files, make sure they are not
applied to the GLFW sources.

*/
