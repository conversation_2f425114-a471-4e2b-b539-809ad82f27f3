// This file is part of libigl, a simple c++ geometry processing library.
// 
// Copyright (C) 2013 <PERSON> <ale<PERSON><PERSON><PERSON><PERSON>@gmail.com>
// 
// This Source Code Form is subject to the terms of the Mozilla Public License 
// v. 2.0. If a copy of the MPL was not distributed with this file, You can 
// obtain one at http://mozilla.org/MPL/2.0/.
#ifndef IGL_PNG_RENDER_TO_PNG_H
#define IGL_PNG_RENDER_TO_PNG_H
#include <igl/igl_inline.h>

#include <string>
namespace igl
{
  namespace png
  {
    //
    // Render current open GL image to .png file
    // Inputs:
    //   png_file  path to output .png file
    //   width  width of scene and resulting image
    //   height height of scene and resulting image
    //   alpha  whether to include alpha channel
    //   fast  sacrifice compression ratio for speed
    // Returns true only if no errors occurred
    //
    // See also: igl/render_to_tga which is faster but writes .tga files
    IGL_INLINE bool render_to_png(
      const std::string png_file,
      const int width,
      const int height,
      const bool alpha = true,
      const bool fast = false);
  }
}

#ifndef IGL_STATIC_LIBRARY
#  include "render_to_png.cpp"
#endif

#endif
