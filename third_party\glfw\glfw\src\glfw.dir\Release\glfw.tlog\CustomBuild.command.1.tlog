^C:\USERS\<USER>\DESKTOP\SQUAREMIND\SQUAREMIND_CPP\THIRD_PARTY\GLFW\GLFW\SRC\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp -BC:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp --check-stamp-file C:/Users/<USER>/Desktop/Squaremind/SquareMind_Cpp/third_party/glfw/glfw/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
